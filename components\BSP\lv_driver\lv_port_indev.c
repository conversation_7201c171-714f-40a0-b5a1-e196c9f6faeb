/**
 * @file lv_port_indev_templ.c
 *
 */

/*Copy this file as "lv_port_indev.c" and set this value to "1" to enable content*/
#if 1

/*********************
 *      INCLUDES
 *********************/
#include "lv_port_indev.h"
#include "../TOUCH/touch.h"
/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/

static void touchpad_init(void);
static void touchpad_read(lv_indev_drv_t * indev_drv, lv_indev_data_t * data);
static bool touchpad_is_pressed(void);
static void touchpad_get_xy(lv_coord_t * x, lv_coord_t * y);

/**********************
 *  STATIC VARIABLES
 **********************/
lv_indev_t * indev_touchpad;

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

void lv_port_indev_init(void)
{
    /**
     * Initialize touchpad input device for LVGL
     */

    static lv_indev_drv_t indev_drv;

    /*------------------
     * Touchpad
     * -----------------*/

    /*Initialize touchpad*/
    touchpad_init();

    /*Register a touchpad input device*/
    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = touchpad_read;

    /* 优化滚动参数以减少抖动 */
    indev_drv.scroll_limit = 5;         /* 减少滚动触发阈值 */
    indev_drv.scroll_throw = 20;        /* 增加滚动惯性阻尼 */
    indev_drv.gesture_min_velocity = 3; /* 降低手势最小速度 */
    indev_drv.gesture_limit = 50;       /* 增加手势识别阈值 */

    indev_touchpad = lv_indev_drv_register(&indev_drv);

    /* 设置触摸设备的读取间隔为10ms，提高响应性 */
    lv_timer_set_period(indev_touchpad->driver->read_timer, 10);
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

/*------------------
 * Touchpad
 * -----------------*/

/*Initialize your touchpad*/
static void touchpad_init(void)
{
    /* Initialize the touchscreen driver */
    tp_dev.init();
}

/*Will be called by the library to read the touchpad*/
static void touchpad_read(lv_indev_drv_t * indev_drv, lv_indev_data_t * data)
{
    static lv_coord_t last_x = 0;
    static lv_coord_t last_y = 0;
    static lv_coord_t filter_x[3] = {0};  /* 滤波缓冲区 */
    static lv_coord_t filter_y[3] = {0};
    static uint8_t filter_index = 0;
    static bool was_pressed = false;

    /*Save the pressed coordinates and the state*/
    if(touchpad_is_pressed()) {
        lv_coord_t raw_x, raw_y;
        touchpad_get_xy(&raw_x, &raw_y);

        /* 简单的移动平均滤波 */
        filter_x[filter_index] = raw_x;
        filter_y[filter_index] = raw_y;
        filter_index = (filter_index + 1) % 3;

        /* 计算平均值 */
        lv_coord_t avg_x = (filter_x[0] + filter_x[1] + filter_x[2]) / 3;
        lv_coord_t avg_y = (filter_y[0] + filter_y[1] + filter_y[2]) / 3;

        /* 如果是新的触摸，直接使用原始值避免延迟 */
        if (!was_pressed) {
            last_x = raw_x;
            last_y = raw_y;
            /* 初始化滤波缓冲区 */
            for (int i = 0; i < 3; i++) {
                filter_x[i] = raw_x;
                filter_y[i] = raw_y;
            }
        } else {
            /* 使用滤波后的值 */
            last_x = avg_x;
            last_y = avg_y;
        }

        data->state = LV_INDEV_STATE_PR;
        was_pressed = true;
    }
    else {
        data->state = LV_INDEV_STATE_REL;
        was_pressed = false;
        filter_index = 0;  /* 重置滤波器 */
    }

    /*Set the coordinates*/
    data->point.x = last_x;
    data->point.y = last_y;
}

/*Return true is the touchpad is pressed*/
static bool touchpad_is_pressed(void)
{
    /* Scan the touchscreen for touch events */
    tp_dev.scan(0);

    /* Check if any touch point is pressed */
    /* tp_dev.sta bit 0-9 represent touch points 0-9 */
    /* If any bit is set, it means that touch point is pressed */
    if (tp_dev.sta & 0x03FF) {  /* Check bits 0-9 for any touch points */
        return true;
    }

    return false;
}

/*Get the x and y coordinates if the touchpad is pressed*/
static void touchpad_get_xy(lv_coord_t * x, lv_coord_t * y)
{
    /* Find the first active touch point and get its coordinates */
    for (int i = 0; i < CT_MAX_TOUCH; i++) {
        if (tp_dev.sta & (1 << i)) {  /* Check if touch point i is pressed */
            /* Get coordinates from the first active touch point */
            *x = tp_dev.x[i];
            *y = tp_dev.y[i];
            return;  /* Return coordinates of first touch point found */
        }
    }

    /* If no touch point is active, return last known coordinates */
    /* This shouldn't happen if touchpad_is_pressed() returned true */
    *x = 0;
    *y = 0;
}

#endif /*Disable/Enable content*/


