/**
 ****************************************************************************************************
 * @file        performance_monitor.c
 * <AUTHOR> Optimization Team
 * @version     V1.0
 * @date        2024-01-01
 * @brief       LVGL性能监控模块实现
 ****************************************************************************************************
 */

#include "performance_monitor.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <stdio.h>
#include <string.h>

static const char *TAG = "perf_monitor";

/* 全局性能监控实例 */
perf_monitor_t g_perf_monitor = {0};

/* 私有变量 */
static uint64_t frame_start_time = 0;
static lv_obj_t *perf_label = NULL;
static lv_timer_t *update_timer = NULL;

/* 前向声明 */
static void perf_monitor_timer_cb(lv_timer_t *timer);

/**
 * @brief       性能监控初始化
 * @param       无
 * @retval      无
 */
void perf_monitor_init(void)
{
    /* 初始化性能监控结构体 */
    memset(&g_perf_monitor, 0, sizeof(perf_monitor_t));
    g_perf_monitor.enabled = true;
    g_perf_monitor.render_time_min = UINT32_MAX;
    g_perf_monitor.last_update_time = esp_timer_get_time() / 1000;
    
    /* 创建性能显示标签 - 显示在右下角 */
    perf_label = lv_label_create(lv_scr_act());
    lv_obj_set_style_text_color(perf_label, lv_color_white(), 0);
    lv_obj_set_style_bg_color(perf_label, lv_color_black(), 0);
    lv_obj_set_style_bg_opa(perf_label, LV_OPA_70, 0);
    lv_obj_set_style_pad_all(perf_label, 5, 0);
    lv_obj_set_style_text_font(perf_label, &lv_font_montserrat_14, 0);  /* 使用较小字体 */
    lv_obj_align(perf_label, LV_ALIGN_BOTTOM_RIGHT, -5, -5);  /* 右下角对齐 */
    lv_label_set_text(perf_label, "FPS: --\nCPU: --%");
    
    /* 创建更新定时器 */
    update_timer = lv_timer_create(perf_monitor_timer_cb, PERF_MON_UPDATE_PERIOD, NULL);
    
    ESP_LOGI(TAG, "Performance monitor initialized");
}

/**
 * @brief       开始帧计时
 * @param       无
 * @retval      无
 */
void perf_monitor_start_frame(void)
{
    if (!g_perf_monitor.enabled) return;
    
    frame_start_time = esp_timer_get_time();
}

/**
 * @brief       结束帧计时
 * @param       无
 * @retval      无
 */
void perf_monitor_end_frame(void)
{
    if (!g_perf_monitor.enabled || frame_start_time == 0) return;
    
    uint64_t frame_end_time = esp_timer_get_time();
    uint32_t render_time = (frame_end_time - frame_start_time) / 1000; /* 转换为毫秒 */
    
    /* 更新统计信息 */
    g_perf_monitor.frame_count++;
    g_perf_monitor.render_time_sum += render_time;
    
    if (render_time > g_perf_monitor.render_time_max) {
        g_perf_monitor.render_time_max = render_time;
    }
    
    if (render_time < g_perf_monitor.render_time_min) {
        g_perf_monitor.render_time_min = render_time;
    }
    
    frame_start_time = 0;
}

/**
 * @brief       更新性能显示回调函数
 * @param       timer: 定时器对象
 * @retval      无
 */
static void perf_monitor_timer_cb(lv_timer_t *timer)
{
    perf_monitor_update_display();
}

/**
 * @brief       更新性能显示
 * @param       无
 * @retval      无
 */
void perf_monitor_update_display(void)
{
    if (!g_perf_monitor.enabled || perf_label == NULL) return;
    
    uint32_t current_time = esp_timer_get_time() / 1000;
    uint32_t time_diff = current_time - g_perf_monitor.last_update_time;
    
    if (time_diff >= PERF_MON_UPDATE_PERIOD && g_perf_monitor.frame_count > 0) {
        /* 计算FPS */
        g_perf_monitor.fps = (float)g_perf_monitor.frame_count * 1000.0f / time_diff;
        
        /* 计算平均渲染时间 */
        g_perf_monitor.avg_render_time = (float)g_perf_monitor.render_time_sum / g_perf_monitor.frame_count;
        
        /* 获取内存使用情况 */
        lv_mem_monitor_t mem_mon;
        lv_mem_monitor(&mem_mon);
        g_perf_monitor.memory_used = mem_mon.total_size - mem_mon.free_size;
        
        if (g_perf_monitor.memory_used > g_perf_monitor.memory_peak) {
            g_perf_monitor.memory_peak = g_perf_monitor.memory_used;
        }
        
        /* 更新显示 */
        char buf[128];
        snprintf(buf, sizeof(buf),
                "FPS: %.1f\n"
                "Render: %.1fms\n"
                "Mem: %uKB\n"
                "Peak: %uKB",
                g_perf_monitor.fps,
                g_perf_monitor.avg_render_time,
                (unsigned int)(g_perf_monitor.memory_used / 1024),
                (unsigned int)(g_perf_monitor.memory_peak / 1024));
        
        lv_label_set_text(perf_label, buf);
        
        /* 重置计数器 */
        g_perf_monitor.frame_count = 0;
        g_perf_monitor.render_time_sum = 0;
        g_perf_monitor.render_time_max = 0;
        g_perf_monitor.render_time_min = UINT32_MAX;
        g_perf_monitor.last_update_time = current_time;
    }
}

/**
 * @brief       启用/禁用性能监控
 * @param       enable: true启用，false禁用
 * @retval      无
 */
void perf_monitor_enable(bool enable)
{
    g_perf_monitor.enabled = enable;
    
    if (perf_label != NULL) {
        if (enable) {
            lv_obj_clear_flag(perf_label, LV_OBJ_FLAG_HIDDEN);
        } else {
            lv_obj_add_flag(perf_label, LV_OBJ_FLAG_HIDDEN);
        }
    }
    
    if (update_timer != NULL) {
        if (enable) {
            lv_timer_resume(update_timer);
        } else {
            lv_timer_pause(update_timer);
        }
    }
}

/**
 * @brief       重置统计信息
 * @param       无
 * @retval      无
 */
void perf_monitor_reset_stats(void)
{
    g_perf_monitor.frame_count = 0;
    g_perf_monitor.render_time_sum = 0;
    g_perf_monitor.render_time_max = 0;
    g_perf_monitor.render_time_min = UINT32_MAX;
    g_perf_monitor.memory_peak = 0;
    g_perf_monitor.last_update_time = esp_timer_get_time() / 1000;
}

/**
 * @brief       获取当前FPS
 * @param       无
 * @retval      FPS值
 */
float perf_monitor_get_fps(void)
{
    return g_perf_monitor.fps;
}

/**
 * @brief       获取平均渲染时间
 * @param       无
 * @retval      平均渲染时间(ms)
 */
float perf_monitor_get_avg_render_time(void)
{
    return g_perf_monitor.avg_render_time;
}

/**
 * @brief       获取内存使用量
 * @param       无
 * @retval      内存使用量(bytes)
 */
uint32_t perf_monitor_get_memory_usage(void)
{
    return g_perf_monitor.memory_used;
}
