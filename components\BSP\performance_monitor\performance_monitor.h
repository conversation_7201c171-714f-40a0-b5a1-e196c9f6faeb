/**
 ****************************************************************************************************
 * @file        performance_monitor.h
 * <AUTHOR> Optimization Team
 * @version     V1.0
 * @date        2024-01-01
 * @brief       LVGL性能监控模块
 ****************************************************************************************************
 */

#ifndef _PERFORMANCE_MONITOR_H
#define _PERFORMANCE_MONITOR_H

#include "lvgl.h"
#include "esp_timer.h"
#include "esp_log.h"

/* 性能监控配置 */
#define PERF_MON_ENABLE         1           /* 启用性能监控 */
#define PERF_MON_UPDATE_PERIOD  1000        /* 更新周期(ms) */
#define PERF_MON_SAMPLE_COUNT   60          /* 采样数量 */

/* 性能统计结构体 */
typedef struct {
    uint32_t frame_count;           /* 帧计数 */
    uint32_t last_update_time;      /* 上次更新时间 */
    uint32_t render_time_sum;       /* 渲染时间总和 */
    uint32_t render_time_max;       /* 最大渲染时间 */
    uint32_t render_time_min;       /* 最小渲染时间 */
    float fps;                      /* 当前FPS */
    float avg_render_time;          /* 平均渲染时间 */
    uint32_t memory_used;           /* 内存使用量 */
    uint32_t memory_peak;           /* 内存峰值 */
    bool enabled;                   /* 监控是否启用 */
} perf_monitor_t;

/* 全局性能监控实例 */
extern perf_monitor_t g_perf_monitor;

/* 函数声明 */
void perf_monitor_init(void);
void perf_monitor_start_frame(void);
void perf_monitor_end_frame(void);
void perf_monitor_update_display(void);
void perf_monitor_enable(bool enable);
void perf_monitor_reset_stats(void);
float perf_monitor_get_fps(void);
float perf_monitor_get_avg_render_time(void);
uint32_t perf_monitor_get_memory_usage(void);

/* 性能监控宏 */
#if PERF_MON_ENABLE
    #define PERF_MON_START_FRAME()      perf_monitor_start_frame()
    #define PERF_MON_END_FRAME()        perf_monitor_end_frame()
    #define PERF_MON_UPDATE()           perf_monitor_update_display()
#else
    #define PERF_MON_START_FRAME()      
    #define PERF_MON_END_FRAME()        
    #define PERF_MON_UPDATE()           
#endif

#endif /* _PERFORMANCE_MONITOR_H */
