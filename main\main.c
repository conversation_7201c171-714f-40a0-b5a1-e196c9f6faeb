
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"
#include "esp_timer.h"
#include "nvs_flash.h"
#include "esp_err.h"
#include "esp_log.h"
#include "led.h"
#include "ltdc.h"
#include "touch.h"
#include "iic.h"
#include "xl9555.h"
#include "lvgl.h"
#include "lv_port_disp.h"
#include "lv_port_indev.h"
#include "gui_guider.h"
#include "performance_monitor.h"


i2c_obj_t i2c0_master;

/* GUI Guider UI对象 */
lv_ui guider_ui;

/* LVGL 时基定时器句柄 */
static esp_timer_handle_t lvgl_tick_timer = NULL;

/**
 * @brief       LVGL时基定时器回调函数，每1ms触发
 * @param       arg: 参数（未使用）
 * @retval      无
 */
static void lv_tick_task(void *arg)
{
    lv_tick_inc(1);  /* LVGL时基递增1ms */
}

/**
 * @brief       初始化LVGL时基定时器
 * @param       无
 * @retval      无
 */
void lvgl_tick_timer_init(void)
{
    const esp_timer_create_args_t timer_args = {
        .callback = &lv_tick_task,
        .arg = NULL,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "lv_tick_timer"
    };

    esp_timer_create(&timer_args, &lvgl_tick_timer);
    esp_timer_start_periodic(lvgl_tick_timer, 1000); /* 1ms触发 */
}

/**
 * @brief       程序入口
 * @param       无
 * @retval      无
 */
void app_main(void)
{
    esp_err_t ret;

    ret = nvs_flash_init();             /* 初始化NVS */

    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }

    i2c0_master = iic_init(I2C_NUM_0);  /* 初始化IIC0 */
    xl9555_init(i2c0_master);           /* 初始化XL9555 */

    /* LVGL初始化 */
    lvgl_tick_timer_init();             /* 初始化LVGL时基 */
    lv_init();                          /* 初始化LVGL图形库 */
    lv_port_disp_init();                /* LVGL显示接口初始化,放在lv_init()的后面 */
    lv_port_indev_init();               /* LVGL输入接口初始化,放在lv_init()的后面 */

    /* 初始化GUI Guider界面 */
    setup_ui(&guider_ui);               /* 设置GUI Guider界面 */

    /* 初始化性能监控 */
    perf_monitor_init();

    /* 优化的LVGL主循环 */
    TickType_t last_wake_time = xTaskGetTickCount();
    const TickType_t task_period = pdMS_TO_TICKS(5);  /* 5ms周期，提升响应性 */

    while (1)
    {
        PERF_MON_START_FRAME();         /* 开始帧计时 */

        uint32_t task_delay = lv_task_handler();  /* LVGL任务管理，返回建议延迟 */

        PERF_MON_END_FRAME();           /* 结束帧计时 */

        /* 使用建议延迟或最小延迟 */
        if (task_delay > 5) {
            vTaskDelay(pdMS_TO_TICKS(task_delay));
        } else {
            vTaskDelayUntil(&last_wake_time, task_period);
        }
    }
}
