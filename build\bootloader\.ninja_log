# ninja log v6
31	503	7771996509440215	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	aca003aa4566b13a
61	516	7771996509743078	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	ac7edd2f1fa809d8
23	526	7771996509356325	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	4f0629051380126c
38	542	7771996509511308	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	e7d977418de16b3
68	611	7771996509806938	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	2e83016f7125c2f6
88	620	7771996510000912	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	743ba3fd0c911355
114	632	7771996510267604	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	b23d6c9fab4fb907
106	641	7771996510187617	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	88908eb5fb33ce7a
96	650	7771996510081307	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	7c89f4ca13349123
46	659	7771996509581289	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	5a7eac5c1c09cc87
138	669	7771996510501903	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	1f718452fc212f5d
162	679	7771996510743651	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	4b11df6725e9d1ba
130	702	7771996510431095	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	e45e10918050dfb0
54	729	7771996509661580	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	89f36d9401723be
121	738	7771996510341094	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	2b3bd89de35630e5
75	747	7771996509880553	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	a4a011e2d9816f80
146	835	7771996510584280	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	ae5cfc05c5a8fd10
154	870	7771996510664285	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	f6662c89472fb1e
506	897	7771996514185067	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	af97d10c98fd6215
516	906	7771996514285062	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	47c208067be9d651
542	1037	7771996514547419	esp-idf/log/liblog.a	9fe540e68b3f479f
650	1086	7771996515630440	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	e84b56dfae6d94b6
660	1095	7771996515720418	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	a8bd9c382a66122e
669	1105	7771996515820526	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	c3ad8b2ae0f2f761
612	1157	7771996515251816	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	f275cb54fd229b45
632	1165	7771996515450052	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	c964cf0e34d93029
703	1175	7771996516157147	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	2a42468e9b6967fa
679	1223	7771996515921064	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	16cdbc0577b5304
729	1232	7771996516418417	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	4ea0fc324b815d8
527	1240	7771996514397423	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	62b9078695bdf7a9
748	1249	7771996516603912	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	a51da225f72d8a59
641	1293	7771996515535558	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	fc84eedbea8e4208
621	1309	7771996515331825	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	f4576ebb998764d7
739	1330	7771996516513910	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	10c2ae2c52d4280f
836	1415	7771996517486849	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	80620778d920c90f
1158	1642	7771996520705354	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	b3d5472d26ef9a3
870	1671	7771996517823633	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	2e1da854db8e92a3
1037	1682	7771996519489137	esp-idf/esp_rom/libesp_rom.a	76a5ded7b2ca549c
1096	1692	7771996520079174	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	693b1cefdf5675af
907	1709	7771996518191662	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	3c045548febdeacf
1223	1719	7771996521354082	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	c572c5c7d1787c64
1106	1728	7771996520190410	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	436ab8398ae337cd
1175	1737	7771996520877013	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	548cd74f6dddaa83
898	1747	7771996518101655	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	6fc297156240c2d6
1087	1755	7771996519992481	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	7d9848c03906a592
1310	1800	7771996522213720	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	958a6f0343d3a6ae
1241	1809	7771996521534294	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	dd68be7af830e54b
1166	1821	7771996520785366	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	d4c198ef93cb0627
1250	1867	7771996521616046	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	8691aaacde663ef1
1642	1966	7771996525545646	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	fb1d783f1fc6999f
1719	2034	7771996526316659	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	531bac33cf71ed00
1682	2083	7771996525941456	esp-idf/esp_common/libesp_common.a	16d17467a2c4e525
1747	2124	7771996526593972	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	f088d255c3206d2d
1710	2133	7771996526226664	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	7be12f9a9c15e345
1800	2151	7771996527127652	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	56faded760bb5c6d
1232	2163	7771996521449888	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	73bad240e72038db
1692	2176	7771996526046654	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	443b96efb7c6069c
1809	2188	7771996527211646	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	4d7101eb17ce0b4e
1672	2199	7771996525848251	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	8c5fa4f5a3433383
1737	2223	7771996526497342	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	d45cd5d41e6f2780
1415	2244	7771996523272202	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	28b9d620aa85dde0
1756	2264	7771996526683959	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	e6322ded129131d8
1821	2289	7771996527336086	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	3218a7a712f7c9f3
1868	2312	7771996527803933	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	dc5ff90409846236
1293	2352	7771996522053741	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	61276d89f376900b
1330	2382	7771996522430144	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	c8e18c3ff045ec9e
1729	2426	7771996526407340	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	18fb62ac85589c67
2152	2437	7771996530636740	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	12889012e50b6dec
1966	2452	7771996528773096	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	514081a2fae92ff2
2133	2460	7771996530454591	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	4ece997a782feb60
2164	2470	7771996530758726	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	8117b1abdd8ae5ad
2177	2503	7771996530898220	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	4c46a4c58ed3afe3
2200	2512	7771996531119044	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	55e4485b45905110
2188	2521	7771996531000626	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	f9302f071792c865
2244	2529	7771996531572003	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	b57f8c2a254bd570
2034	2553	7771996529465923	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	977179509d5045cf
2223	2564	7771996531341474	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	391f82e2f04a00c5
2083	2603	7771996529958005	esp-idf/esp_hw_support/libesp_hw_support.a	a22e20a51d2bd2b0
2264	2624	7771996531768577	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	728e213496b12ab9
2289	2634	7771996532002923	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	8b1a3e9c13088575
2313	2643	7771996532251937	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	36284a70d4d93c65
2352	2665	7771996532651846	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	1d6d4d64b28f05f5
2382	2675	7771996532943373	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	c6232ff47fd254ba
2427	2756	7771996533383018	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	79a690756593a85d
2470	2767	7771996533814049	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	cd88ef30ced30729
2452	2768	7771996533643213	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	d68e5dc1f90372f1
2437	2770	7771996533498367	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	622b247c7ed7b2f3
2461	2771	7771996533723629	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	2f7eee66471fe57
2529	2804	7771996534416119	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	1e186cd9fac3c7b3
2512	2815	7771996534250064	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	f08f3d5dee8b8c0
2521	2816	7771996534334246	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	88fd07eda1bf1b0d
2554	2824	7771996534663863	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	c54abca9198becba
2503	2827	7771996534157561	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	217868e2020e27fc
2676	2833	7771996537267015	project_elf_src_esp32s3.c	b8d0ff71e4b9b057
2676	2833	7771996537267015	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/project_elf_src_esp32s3.c	b8d0ff71e4b9b057
2624	2846	7771996535360854	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	f9f82b55bf0e4872
2643	2873	7771996535540457	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	cd9fe949a67de97
2564	2882	7771996534772868	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	2383bf602ff145a4
2634	2900	7771996535460029	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	1c61d55442d51011
2603	2911	7771996535155725	esp-idf/esp_system/libesp_system.a	6ba3db5ebc7222d0
2666	2917	7771996535781832	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	8fd2f941b1abe2d7
2833	2938	7771996537448026	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	f6defcaf21da4b68
2757	2979	7771996536679586	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	834482eb125c9057
2911	3052	7771996538230963	esp-idf/efuse/libefuse.a	b027f75ac9dc6d3a
2125	3095	7771996530372590	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	75d15f451b03cdd4
3053	3232	7771996539635182	esp-idf/bootloader_support/libbootloader_support.a	41abbb3847180a9a
3232	3355	7771996541443804	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	7704240e3fc65260
3355	3470	7771996542665289	esp-idf/spi_flash/libspi_flash.a	f2f1321b4133f01b
3470	3602	7771996543831788	esp-idf/hal/libhal.a	4ecd0b7d65881b5c
3602	3719	7771996545121858	esp-idf/micro-ecc/libmicro-ecc.a	564c426ed7c13bba
3719	3901	7771996546309231	esp-idf/soc/libsoc.a	be819c31531e0d15
3901	4020	7771996548126116	esp-idf/xtensa/libxtensa.a	1e98b69c813b3b67
4020	4139	7771996549303070	esp-idf/main/libmain.a	5d9eeaf9cc3081fe
4139	4369	7771996550482333	bootloader.elf	7788bcbde2f2077
4369	4670	7771996555759482	.bin_timestamp	d1e10164ef537244
4369	4670	7771996555759482	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/.bin_timestamp	d1e10164ef537244
4670	4763	7771996555805697	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
4670	4763	7771996555805697	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
