# ninja log v6
36	197	7771225367812695	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	ac7edd2f1fa809d8
66	243	7771225368108363	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	88908eb5fb33ce7a
13	252	7771225367581251	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	4f0629051380126c
18	262	7771225367596162	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	aca003aa4566b13a
22	270	7771225367679156	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	e7d977418de16b3
97	280	7771225368431067	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	1f718452fc212f5d
59	295	7771225368044408	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	7c89f4ca13349123
52	303	7771225367974055	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	743ba3fd0c911355
27	311	7771225367707396	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	5a7eac5c1c09cc87
42	349	7771225367863511	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	2e83016f7125c2f6
73	359	7771225368188008	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	b23d6c9fab4fb907
47	377	7771225367922069	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	a4a011e2d9816f80
81	390	7771225368258585	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	2b3bd89de35630e5
88	432	7771225368315367	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	e45e10918050dfb0
32	450	7771225367768794	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	89f36d9401723be
119	458	7771225368646774	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	4b11df6725e9d1ba
199	505	7771225369445757	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	af97d10c98fd6215
112	515	7771225368577049	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	f6662c89472fb1e
105	534	7771225368490506	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	ae5cfc05c5a8fd10
243	566	7771225369885529	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	47c208067be9d651
271	591	7771225370161177	esp-idf/log/liblog.a	9fe540e68b3f479f
312	673	7771225370576234	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	e84b56dfae6d94b6
350	754	7771225370949742	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	a8bd9c382a66122e
359	809	7771225371041937	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	c3ad8b2ae0f2f761
295	818	7771225370404522	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	c964cf0e34d93029
391	827	7771225371364332	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	2a42468e9b6967fa
303	839	7771225370476967	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	fc84eedbea8e4208
378	848	7771225371234134	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	16cdbc0577b5304
262	856	7771225370083721	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	f275cb54fd229b45
433	875	7771225371778957	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	4ea0fc324b815d8
458	883	7771225372041332	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	a51da225f72d8a59
280	895	7771225370254407	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	f4576ebb998764d7
253	939	7771225369983502	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	62b9078695bdf7a9
450	951	7771225371953214	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	10c2ae2c52d4280f
591	1033	7771225373355883	esp-idf/esp_rom/libesp_rom.a	76a5ded7b2ca549c
515	1044	7771225372609173	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	2e1da854db8e92a3
505	1054	7771225372506901	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	80620778d920c90f
535	1134	7771225372800530	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	6fc297156240c2d6
754	1143	7771225374985938	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	693b1cefdf5675af
673	1173	7771225374173332	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	7d9848c03906a592
810	1189	7771225375553277	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	436ab8398ae337cd
819	1215	7771225375646962	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	b3d5472d26ef9a3
840	1249	7771225375854318	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	548cd74f6dddaa83
566	1257	7771225373106512	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	3c045548febdeacf
848	1275	7771225375929553	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	c572c5c7d1787c64
940	1284	7771225376847300	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	958a6f0343d3a6ae
827	1320	7771225375727812	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	d4c198ef93cb0627
1054	1366	7771225377994860	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	fb1d783f1fc6999f
1033	1389	7771225377783028	esp-idf/esp_common/libesp_common.a	16d17467a2c4e525
876	1400	7771225376216018	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	dd68be7af830e54b
884	1443	7771225376290419	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	8691aaacde663ef1
1189	1507	7771225379346743	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	531bac33cf71ed00
1144	1515	7771225378881075	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	443b96efb7c6069c
1174	1538	7771225379187557	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	7be12f9a9c15e345
1258	1556	7771225380034233	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	f088d255c3206d2d
1135	1598	7771225378802167	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	8c5fa4f5a3433383
857	1608	7771225376024167	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	73bad240e72038db
1250	1617	7771225379952458	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	d45cd5d41e6f2780
1284	1642	7771225380294644	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	56faded760bb5c6d
1321	1652	7771225380658752	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	4d7101eb17ce0b4e
1045	1663	7771225377890851	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	28b9d620aa85dde0
1367	1672	7771225381112277	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	3218a7a712f7c9f3
1275	1730	7771225380206827	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	e6322ded129131d8
895	1740	7771225376410665	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	61276d89f376900b
1556	1749	7771225383011348	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	12889012e50b6dec
1389	1766	7771225381342087	esp-idf/esp_hw_support/libesp_hw_support.a	a22e20a51d2bd2b0
1538	1774	7771225382838083	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	4ece997a782feb60
952	1795	7771225376975595	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	c8e18c3ff045ec9e
1400	1804	7771225381445254	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	dc5ff90409846236
1598	1816	7771225383433694	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	8117b1abdd8ae5ad
1443	1824	7771225381881157	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	514081a2fae92ff2
1215	1849	7771225379608803	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	18fb62ac85589c67
1663	1871	7771225384084300	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	b57f8c2a254bd570
1652	1879	7771225383961171	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	391f82e2f04a00c5
1617	1888	7771225383630428	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	f9302f071792c865
1608	1898	7771225383536705	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	4c46a4c58ed3afe3
1642	1909	7771225383878496	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	55e4485b45905110
1672	1918	7771225384170424	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	728e213496b12ab9
1508	1926	7771225382520074	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	977179509d5045cf
1730	1961	7771225384759411	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	8b1a3e9c13088575
1775	1980	7771225385202681	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	c6232ff47fd254ba
1740	1989	7771225384859420	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	36284a70d4d93c65
1749	2003	7771225384945442	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	1d6d4d64b28f05f5
1796	2040	7771225385414460	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	79a690756593a85d
1804	2049	7771225385491660	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	622b247c7ed7b2f3
1816	2050	7771225385611850	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	d68e5dc1f90372f1
1849	2051	7771225385937039	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	cd88ef30ced30729
1824	2087	7771225385697505	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	2f7eee66471fe57
1898	2089	7771225386439867	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	1e186cd9fac3c7b3
1910	2094	7771225386548864	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	c54abca9198becba
1879	2096	7771225386240386	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	f08f3d5dee8b8c0
1871	2103	7771225386162154	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	217868e2020e27fc
2003	2104	7771225388408320	project_elf_src_esp32s3.c	b8d0ff71e4b9b057
2003	2104	7771225388408320	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/project_elf_src_esp32s3.c	b8d0ff71e4b9b057
1926	2111	7771225386722976	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	f9f82b55bf0e4872
1766	2111	7771225385116521	esp-idf/esp_system/libesp_system.a	6ba3db5ebc7222d0
1889	2119	7771225386334570	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	88fd07eda1bf1b0d
1918	2120	7771225386638649	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	2383bf602ff145a4
1981	2144	7771225387256331	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	cd9fe949a67de97
1961	2164	7771225387063197	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	1c61d55442d51011
1989	2186	7771225387351662	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	8fd2f941b1abe2d7
2104	2192	7771225388491797	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	f6defcaf21da4b68
2041	2229	7771225387862612	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	834482eb125c9057
2111	2266	7771225388568284	esp-idf/efuse/libefuse.a	b027f75ac9dc6d3a
1516	2383	7771225382609172	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	75d15f451b03cdd4
2266	2443	7771225390107401	esp-idf/bootloader_support/libbootloader_support.a	41abbb3847180a9a
2443	2560	7771225391882755	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	7704240e3fc65260
2560	2675	7771225393052295	esp-idf/spi_flash/libspi_flash.a	f2f1321b4133f01b
2675	2801	7771225394203888	esp-idf/hal/libhal.a	4ecd0b7d65881b5c
2801	2921	7771225395469099	esp-idf/micro-ecc/libmicro-ecc.a	564c426ed7c13bba
2921	3101	7771225396664672	esp-idf/soc/libsoc.a	be819c31531e0d15
3101	3220	7771225398457297	esp-idf/xtensa/libxtensa.a	1e98b69c813b3b67
3221	3339	7771225399662682	esp-idf/main/libmain.a	5d9eeaf9cc3081fe
3339	3569	7771225400838242	bootloader.elf	7788bcbde2f2077
3569	3864	7771225406030756	.bin_timestamp	d1e10164ef537244
3569	3864	7771225406030756	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/.bin_timestamp	d1e10164ef537244
3865	3957	7771225406096817	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
3865	3957	7771225406096817	C:/Users/<USER>/Desktop/ESP32/Code/4.Onenet/project/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8424435dbb1c5e6d
